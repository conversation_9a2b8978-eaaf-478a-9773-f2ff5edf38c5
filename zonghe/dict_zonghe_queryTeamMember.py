#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
dict_zonghe_queryTeamMember.py - 综合查询团队信息数据获取脚本
功能：从dict系统获取团队成员信息并入库到MySQL数据库

API接口：http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryTeamMember
数据表：dict_zonghe_queryTeamMember
"""

import requests
import json
import pymysql
import time
from datetime import datetime
import sys
import traceback

# 数据库配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 3306,
    'user': 'root',
    'password': 'cmcc12345',
    'database': 'dict_spider',
    'charset': 'utf8mb4'
}

# API配置
API_URL = 'http://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryTeamMember'

def load_cookies_from_file(filename='cookies.txt'):
    """从JSON格式的cookies.txt文件加载Cookie"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {len(cookies)} 个Cookie")
        return cookies
    except FileNotFoundError:
        print(f"[错误] Cookie文件 {filename} 不存在")
        return None
    except json.JSONDecodeError as e:
        print(f"[错误] Cookie文件JSON格式错误: {e}")
        return None
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        return None

def get_project_ids():
    """从v_distinct_project_id视图获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查询v_distinct_project_id视图
        cursor.execute("SELECT project_id FROM v_distinct_project_id")
        project_ids = [row[0] for row in cursor.fetchall()]
        
        print(f"[信息] 从v_distinct_project_id视图获取到 {len(project_ids)} 个项目ID")
        return project_ids
        
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {e}")
        # 如果视图不存在，尝试从其他表获取
        try:
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != ''")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {len(project_ids)} 个项目ID")
            return project_ids
        except Exception as e2:
            print(f"[错误] 从备用表获取项目ID也失败: {e2}")
            return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies.items()])
    
    headers = {
        'Host': 'dict.gmcc.net:30722',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
        'Pragma': 'no-cache',
        'Cache-Control': 'no-cache',
        'x-session-staffname': cookies.get('userName', 'dengyong'),
        'x-session-regionid': '999',
        'x-session-sysusercode': cookies.get('userCode', 'dengyong'),
        'x-session-staffid': '1000032328',
        'Origin': 'http://dict.gmcc.net:30722',
        'Referer': 'http://dict.gmcc.net:30722/dictWeb/gdydFlowPath/fullView/',
        'Accept-Language': 'zh-CN,zh;q=0.9,ee;q=0.8',
        'Cookie': cookie_str
    }
    return headers

def build_request_payload(project_id):
    """构建请求载荷"""
    payload = {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": "liaochulin"
                }
            },
            "BODY": {
                "IN_PARAM": {
                    "PROJECT_ID": project_id
                },
                "PAGE_INFO": {
                    "PAGE_NUM": 1,
                    "PAGE_SIZE": 100
                }
            }
        }
    }
    return payload

def fetch_team_member_data(project_id, cookies):
    """获取团队成员数据"""
    try:
        headers = build_request_headers(cookies)
        payload = build_request_payload(project_id)
        
        print(f"[信息] 正在获取项目 {project_id} 的团队信息...")
        
        response = requests.post(API_URL, 
                               data=json.dumps(payload), 
                               headers=headers, 
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            # 检查响应状态
            return_code = data.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            return_msg = data.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '')
            
            if return_code == '0' and return_msg == 'OK':
                out_data = data.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {})
                team_members = out_data.get('OUT_PARAM', [])
                
                print(f"[成功] 项目 {project_id} 获取到 {len(team_members)} 个团队成员")
                return team_members
            else:
                print(f"[警告] 项目 {project_id} API返回错误: {return_msg}")
                return []
        else:
            print(f"[错误] 项目 {project_id} HTTP请求失败: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"[错误] 获取项目 {project_id} 团队数据失败: {e}")
        return []

def save_team_members_to_db(project_id, team_members):
    """保存团队成员数据到数据库"""
    if not team_members:
        return 0
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 准备插入SQL
        insert_sql = """
        INSERT INTO dict_zonghe_queryTeamMember 
        (PROJECT_ID, TEAM_TYPE, STAFF_NAME, POST_NAME, DEPT_NAME, PHONE, 
         JOIN_TIME, STAFF_ID, MEMBER_ID, DEPT_ID, POST_ID, STAFF_TYPE, 
         IS_CHIEF, CONTRIBUTE_RATIO, import_time)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        # 准备数据
        insert_data = []
        for member in team_members:
            data_row = (
                project_id,
                member.get('TEAM_TYPE', ''),
                member.get('STAFF_NAME', ''),
                member.get('POST_NAME', ''),
                member.get('DEPT_NAME', ''),
                member.get('PHONE', ''),
                member.get('JOIN_TIME', ''),
                member.get('STAFF_ID', ''),
                member.get('MEMBER_ID', ''),
                member.get('DEPT_ID', ''),
                member.get('POST_ID', ''),
                member.get('STAFF_TYPE', ''),
                member.get('IS_CHIEF', ''),
                member.get('CONTRIBUTE_RATIO', ''),
                datetime.now()
            )
            insert_data.append(data_row)
        
        # 批量插入
        cursor.executemany(insert_sql, insert_data)
        conn.commit()
        
        print(f"[成功] 项目 {project_id} 保存了 {len(insert_data)} 条团队成员记录")
        return len(insert_data)
        
    except Exception as e:
        print(f"[错误] 保存项目 {project_id} 团队数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
        return 0
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def clear_table():
    """清空数据表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        cursor.execute("TRUNCATE TABLE dict_zonghe_queryTeamMember")
        conn.commit()
        
        print("[信息] 数据表已清空")
        return True
        
    except Exception as e:
        print(f"[错误] 清空数据表失败: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("综合查询团队信息数据同步程序")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies_from_file()
    if not cookies:
        print("[错误] 无法加载Cookie，请运行 login2cookie.py 更新Cookie")
        return
    
    # 获取项目ID列表
    project_ids = get_project_ids()
    if not project_ids:
        print("[错误] 无法获取项目ID列表")
        return
    
    # 清空数据表
    if not clear_table():
        print("[错误] 清空数据表失败")
        return
    
    # 统计信息
    total_projects = len(project_ids)
    success_count = 0
    total_records = 0
    
    print(f"[信息] 开始处理 {total_projects} 个项目...")
    
    # 轮询处理每个项目
    for i, project_id in enumerate(project_ids, 1):
        print(f"\n[进度] {i}/{total_projects} - 处理项目: {project_id}")
        
        # 获取团队数据
        team_members = fetch_team_member_data(project_id, cookies)
        
        if team_members:
            # 保存到数据库
            saved_count = save_team_members_to_db(project_id, team_members)
            if saved_count > 0:
                success_count += 1
                total_records += saved_count
        
        # 添加延迟避免请求过快
        time.sleep(1)
    
    # 输出统计结果
    print("\n" + "=" * 60)
    print("数据同步完成")
    print(f"总项目数: {total_projects}")
    print(f"成功项目数: {success_count}")
    print(f"总记录数: {total_records}")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n[信息] 程序被用户中断")
    except Exception as e:
        print(f"\n[错误] 程序执行失败: {e}")
        traceback.print_exc()
