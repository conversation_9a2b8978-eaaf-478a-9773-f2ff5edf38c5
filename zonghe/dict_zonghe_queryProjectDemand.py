#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
综合查询项目需求信息爬虫程序
对应接口：saleCenterApp/formulation/queryProjectDemand
功能：从dict系统获取项目需求信息并同步到MySQL数据库
"""

import sys
import os
import json
import time
import requests
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
import pymysql
from datetime import datetime

# 添加配置文件路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'dict_romte'))
from dict_romte.config import get_db_config, get_login_credentials

# 数据库配置
DB_CONFIG = get_db_config('default')

# API配置
API_URL = "https://dict.gmcc.net:30722/dictWeb/gatewayService/saleCenterApp/formulation/queryProjectDemand"

def load_cookies():
    """从cookies.txt文件加载cookie"""
    try:
        with open('../cookies.txt', 'r', encoding='utf-8') as f:
            cookie_data = json.load(f)
        
        cookies = {}
        for cookie in cookie_data.get('cookies', []):
            cookies[cookie['name']] = cookie['value']
        
        print(f"[信息] 成功加载 {len(cookies)} 个Cookie")
        return cookies
    except Exception as e:
        print(f"[错误] 加载Cookie失败: {e}")
        return {}

def get_project_ids():
    """从v_distinct_project_id视图获取项目ID列表"""
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 查询v_distinct_project_id视图
        cursor.execute("SELECT project_id FROM v_distinct_project_id")
        project_ids = [row[0] for row in cursor.fetchall()]
        
        print(f"[信息] 从v_distinct_project_id视图获取到 {len(project_ids)} 个项目ID")
        return project_ids
        
    except Exception as e:
        print(f"[错误] 获取项目ID失败: {e}")
        # 如果视图不存在，尝试从其他表获取
        try:
            cursor.execute("SELECT DISTINCT `项目编码` FROM sign_data_detail WHERE `项目编码` IS NOT NULL AND `项目编码` != ''")
            project_ids = [row[0] for row in cursor.fetchall()]
            print(f"[信息] 从sign_data_detail表获取到 {len(project_ids)} 个项目ID")
            return project_ids
        except Exception as e2:
            print(f"[错误] 从备用表获取项目ID也失败: {e2}")
            return []
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def build_request_headers(cookies):
    """构建请求头"""
    cookie_str = "; ".join([f"{name}={value}" for name, value in cookies.items()])
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Host': 'dict.gmcc.net:30722',
        'Origin': 'https://dict.gmcc.net:30722',
        'Pragma': 'no-cache',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Cookie': cookie_str
    }
    return headers

def build_request_data(project_id, login_no):
    """构建请求数据"""
    return {
        "ROOT": {
            "HEADER": {
                "OPR_INFO": {
                    "LOGIN_NO": login_no
                }
            },
            "BODY": {
                "PROJECT_ID": project_id
            }
        }
    }

def query_project_demand(project_id, cookies, login_no):
    """查询单个项目的需求信息"""
    try:
        headers = build_request_headers(cookies)
        data = build_request_data(project_id, login_no)
        
        response = requests.post(API_URL, headers=headers, json=data, timeout=30, verify=False)
        
        if response.status_code == 200:
            result = response.json()
            
            # 检查返回码
            return_code = result.get('ROOT', {}).get('BODY', {}).get('RETURN_CODE', '')
            if return_code == '0':
                return result.get('ROOT', {}).get('BODY', {}).get('OUT_DATA', {})
            else:
                error_msg = result.get('ROOT', {}).get('BODY', {}).get('RETURN_MSG', '未知错误')
                print(f"[警告] 项目 {project_id} 查询失败: {error_msg}")
                return None
        else:
            print(f"[错误] 项目 {project_id} HTTP请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"[错误] 项目 {project_id} 查询异常: {e}")
        return None

def save_to_database(project_data_list):
    """保存数据到数据库"""
    if not project_data_list:
        print("[警告] 没有数据需要保存")
        return
    
    try:
        conn = pymysql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        # 清空原有数据
        cursor.execute("DELETE FROM dict_zonghe_queryProjectDemand")
        print(f"[信息] 已清空原有数据")
        
        # 准备插入SQL
        insert_sql = """
        INSERT INTO dict_zonghe_queryProjectDemand (
            INPUT_LOGIN_NO, INPUT_PROJECT_ID, SUPPORT_APPLICATION_TIME, REGION_CODE, GOV_ENTER_CENTER,
            GRID_MEMBER, MAIN_PROCESS_TYPE, MAIN_PROCESS_TYPE_DESC, PROJECT_CONTRACT_TYPE, PROJECT_CONTRACT_TYPE_NAME,
            ELECTION_MODE, ELECTION_MODE_NAME, CONTRACT_PERIOD, CONTRACT_DURATION, IT_REQUIREMENTS_DESC,
            CT_REQUIREMENTS_DESC, IS_CONNECTED_OPP, CUST_ID, ESTIMATED_AMOUNT, PROJECT_NAME,
            REGION_CODE_DESC, REQUIREMENTS_TITEL, ORG_DESC, SALE_OPP_ID, THE_DEGREE_OF_URGENCY,
            BID_FLAG, BID_FLAG_NAME, YS_FLAG, GW_NAME, GW_NO, PROJECT_TYPE,
            PRE_SOLUTION_FINISH_TIME, DISCLOSURE_START_TIME, DISCLOSURE_FINISH_TIME,
            PUBM_COUNTY_ID, PUBM_COUNTY_NAME, REGION_ID, ONEDICT_REGION_ID,
            RUN_IP, REQUEST_ID, RETURN_CODE, RETURN_MSG, USER_MSG, DETAIL_MSG
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
            %s, %s, %s, %s
        )
        """
        
        # 批量插入数据
        for data in project_data_list:
            demand_info = data['demand_info']
            system_info = data['system_info']
            
            # 提取嵌套对象信息
            pm_county_info = demand_info.get('PM_PUBM_COUNTY_INFO_OUT', {})
            
            values = (
                data['input_login_no'], data['input_project_id'],
                demand_info.get('SUPPORT_APPLICATION_TIME'), demand_info.get('REGION_CODE'), demand_info.get('GOV_ENTER_CENTER'),
                demand_info.get('GRID_MEMBER'), demand_info.get('MAIN_PROCESS_TYPE'), demand_info.get('MAIN_PROCESS_TYPE_DESC'),
                demand_info.get('PROJECT_CONTRACT_TYPE'), demand_info.get('PROJECT_CONTRACT_TYPE_NAME'),
                demand_info.get('ELECTION_MODE'), demand_info.get('ELECTION_MODE_NAME'), demand_info.get('CONTRACT_PERIOD'),
                demand_info.get('CONTRACT_DURATION'), demand_info.get('IT_REQUIREMENTS_DESC'), demand_info.get('CT_REQUIREMENTS_DESC'),
                demand_info.get('IS_CONNECTED_OPP'), demand_info.get('CUST_ID'), demand_info.get('ESTIMATED_AMOUNT'),
                demand_info.get('PROJECT_NAME'), demand_info.get('REGION_CODE_DESC'), demand_info.get('REQUIREMENTS_TITEL'),
                demand_info.get('ORG_DESC'), demand_info.get('SALE_OPP_ID'), demand_info.get('THE_DEGREE_OF_URGENCY'),
                demand_info.get('BID_FLAG'), demand_info.get('BID_FLAG_NAME'), demand_info.get('YS_FLAG'),
                demand_info.get('GW_NAME'), demand_info.get('GW_NO'), demand_info.get('PROJECT_TYPE'),
                demand_info.get('PRE_SOLUTION_FINISH_TIME'), demand_info.get('DISCLOSURE_START_TIME'), demand_info.get('DISCLOSURE_FINISH_TIME'),
                pm_county_info.get('PUBM_COUNTY_ID'), pm_county_info.get('PUBM_COUNTY_NAME'), 
                pm_county_info.get('REGION_ID'), pm_county_info.get('ONEDICT_REGION_ID'),
                system_info.get('RUN_IP'), system_info.get('REQUEST_ID'), system_info.get('RETURN_CODE'),
                system_info.get('RETURN_MSG'), system_info.get('USER_MSG'), system_info.get('DETAIL_MSG')
            )
            
            cursor.execute(insert_sql, values)
        
        conn.commit()
        print(f"[成功] 成功保存 {len(project_data_list)} 条项目需求信息数据")
        
    except Exception as e:
        print(f"[错误] 保存数据失败: {e}")
        if 'conn' in locals():
            conn.rollback()
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()

def main():
    """主函数"""
    print("=" * 60)
    print("综合查询项目需求信息爬虫程序启动")
    print("=" * 60)
    
    # 加载Cookie
    cookies = load_cookies()
    if not cookies:
        print("[错误] Cookie加载失败，请先运行 login2cookie.py 获取Cookie")
        return
    
    # 获取登录用户名
    login_no, _, _ = get_login_credentials()
    
    # 获取项目ID列表
    project_ids = get_project_ids()
    if not project_ids:
        print("[错误] 未获取到项目ID列表")
        return
    
    print(f"[信息] 开始处理 {len(project_ids)} 个项目")
    
    project_data_list = []
    success_count = 0
    error_count = 0
    
    for i, project_id in enumerate(project_ids, 1):
        print(f"[进度] {i}/{len(project_ids)} 处理项目: {project_id}")
        
        # 查询项目需求信息
        demand_info = query_project_demand(project_id, cookies, login_no)
        
        if demand_info:
            # 提取系统信息
            system_info = {
                'RUN_IP': demand_info.get('RUN_IP'),
                'REQUEST_ID': demand_info.get('REQUEST_ID'),
                'RETURN_CODE': demand_info.get('RETURN_CODE'),
                'RETURN_MSG': demand_info.get('RETURN_MSG'),
                'USER_MSG': demand_info.get('USER_MSG'),
                'DETAIL_MSG': demand_info.get('DETAIL_MSG')
            }
            
            project_data_list.append({
                'input_login_no': login_no,
                'input_project_id': project_id,
                'demand_info': demand_info,
                'system_info': system_info
            })
            success_count += 1
        else:
            error_count += 1
        
        # 每处理10个项目休息一下
        if i % 10 == 0:
            time.sleep(1)
    
    print(f"\n[统计] 成功: {success_count}, 失败: {error_count}")
    
    # 保存到数据库
    if project_data_list:
        save_to_database(project_data_list)
    
    print("=" * 60)
    print("综合查询项目需求信息爬虫程序完成")
    print("=" * 60)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "-all":
        main()
    else:
        print("使用方法: python dict_zonghe_queryProjectDemand.py -all")
        print("说明: -all 参数表示轮询所有项目数据同步入库")
